import 'package:builder_konnect_mgt/core/core.dart';

class EmptyListState extends StatelessWidget {
  const EmptyListState({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: Sizer.width(16)),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SvgPicture.asset(AppSvgs.empty),
          YBox(10),
          Text(
            "No Orders Assigned Yet",
            style: AppTypography.text18.medium.copyWith(
              fontWeight: FontWeight.w500,
              color: AppColors.black.withValues(alpha: 0.85),
            ),
          ),
          YBox(4),
          Text(
            "You currently have no orders to fulfill. \nOnce an order is assigned to you, it will appear here with all the details you need to get started.",
            textAlign: TextAlign.center,
            style: AppTypography.text14.medium.copyWith(
              fontWeight: FontWeight.w500,
              color: AppColors.black.withValues(alpha: 0.45),
            ),
          ),
        ],
      ),
    );
  }
}
