import 'package:builder_konnect_mgt/core/core.dart';

class TimelineWidget extends StatelessWidget {
  const TimelineWidget({
    super.key,
    required this.shortDesc,
    required this.longDesc,
    this.time,
    required this.images,
  });

  final String shortDesc;
  final String longDesc;
  final String? time;
  final List<String> images;

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          // mainAxisSize: MainAxisSize.min,
          children: [
            time != null
                ? Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      SvgPicture.asset(AppSvgs.checkbox),
                      Container(
                        width: 1,
                        height: Sizer.height(80),
                        color: AppColors.primaryBlue,
                      ),
                    ],
                  )
                : Column(
                    children: [
                      Container(
                        height: Sizer.height(16),
                        width: Sizer.width(16),
                        decoration: BoxDecoration(
                          color: AppColors.neutral2,
                          border: Border.all(
                            color: AppColors.neutral5,
                            width: 1,
                          ),
                          borderRadius: BorderRadius.circular(Sizer.radius(2)),
                        ),
                      ),
                      Container(
                        width: 1,
                        height: Sizer.height(80),
                        color: AppColors.neutral5,
                      ),
                    ],
                  )
          ],
        ),
        XBox(10),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                shortDesc,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: AppTypography.text14
                    .withCustomColor(AppColors.black.withValues(alpha: 0.85)),
              ),
              YBox(2),
              Text(
                longDesc,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                style: AppTypography.text12.withCustomColor(
                  AppColors.black.withValues(alpha: 0.45),
                ),
              ),
              YBox(4),
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  // Text(
                  //   "MAR 12 2025",
                  //   style: AppTypography.text10.withCustomColor(
                  //     AppColors.black.withValues(alpha: 0.45),
                  //   ),
                  // ),
                  // XBox(8),
                  // Container(
                  //   height: Sizer.height(8),
                  //   width: Sizer.width(8),
                  //   decoration: BoxDecoration(
                  //     shape: BoxShape.circle,
                  //     color: AppColors.neutral5,
                  //   ),
                  // ),
                  // XBox(8),
                  if (time != null)
                    Text(
                      time ?? "",
                      style: AppTypography.text10.withCustomColor(
                        AppColors.black.withValues(alpha: 0.45),
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
        XBox(10),
        if (images.isNotEmpty)
          InkWell(
            onTap: () {
              printty("images ${images.toString()}");
              showDialog(
                context: context,
                builder: (_) => Dialog(
                  backgroundColor: Colors.transparent,
                  insetPadding: EdgeInsets.zero,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      SizedBox(
                        height: Sizer.screenHeight * 0.6,
                        child: ListView.separated(
                          scrollDirection: Axis.horizontal,
                          itemCount: images.length,
                          separatorBuilder: (_, __) => XBox(16),
                          padding: EdgeInsets.zero,
                          itemBuilder: (ctx, i) {
                            return Container(
                              padding: EdgeInsets.only(
                                left: images.length == 1 ? 0 : Sizer.width(16),
                                right: images.length == 1
                                    ? 0
                                    : i == images.length - 1
                                        ? Sizer.width(16)
                                        : 0,
                              ),
                              // width: Sizer.screenWidth,
                              width: images.length == 1
                                  ? Sizer.screenWidth
                                  : Sizer.screenWidth * 0.9,
                              child: MyCachedNetworkImage(
                                imageUrl: images[i],
                                fit: BoxFit.cover,
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
            child: Text(
              "View Images",
              style:
                  AppTypography.text12.withCustomColor(AppColors.primaryBlue),
            ),
          ),
      ],
    );
  }
}
