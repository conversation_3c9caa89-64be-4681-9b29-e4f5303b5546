import 'dart:io';

import 'package:builder_konnect_mgt/core/core.dart';
import 'package:builder_konnect_mgt/ui/components/components.dart';
import 'package:percent_indicator/linear_percent_indicator.dart';

class LogComplaintModal extends ConsumerStatefulWidget {
  const LogComplaintModal({
    super.key,
    required this.deliveryId,
  });

  final String deliveryId;

  @override
  ConsumerState<LogComplaintModal> createState() => _LogComplaintModalState();
}

class _LogComplaintModalState extends ConsumerState<LogComplaintModal> {
  final commentC = TextEditingController();
  final commentF = FocusNode();

  final List<File> _imageFiles = [];
  final List<String> _uploadedUrls = [];
  bool loadingImage = false;
  bool _uploadsComplete = false;

  final List<String> _reasons = [
    "Item mismatch",
    "Damaged product",
    "Poor product quality",
    "Suspicious or tampered package",
    "Late delivery from vendor",
    "Other"
  ];
  int _selectedReasonIndex = 0;

  @override
  void dispose() {
    commentC.dispose();
    commentF.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        left: Sizer.width(16),
        right: Sizer.width(16),
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      child: ConstrainedBox(
        constraints: BoxConstraints(
          maxHeight: Sizer.screenHeight * 0.8,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            YBox(20),
            Text("Log Complaint", style: AppTypography.text16.medium),
            Flexible(
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    YBox(24),
                    Text("Select Reason", style: AppTypography.text14),
                    YBox(16),
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      children: _reasons.map((e) {
                        return Padding(
                          padding: EdgeInsets.only(
                              bottom: _reasons.indexOf(e) < _reasons.length - 1
                                  ? Sizer.height(12)
                                  : 0),
                          child: ListTileSelector(
                            title: e,
                            isSelected:
                                _reasons.indexOf(e) == _selectedReasonIndex,
                            onTap: () {
                              _selectedReasonIndex = _reasons.indexOf(e);
                              setState(() {});
                            },
                          ),
                        );
                      }).toList(),
                    ),
                    AnimatedSize(
                      duration: const Duration(milliseconds: 400),
                      curve: Curves.easeInOut,
                      child: _selectedReasonIndex == _reasons.length - 1
                          ? Padding(
                              padding: EdgeInsets.only(top: Sizer.height(16)),
                              child: CustomTextField(
                                controller: commentC,
                                focusNode: commentF,
                                showLabelHeader: true,
                                labelText: "Enter Reason",
                                hintText: "Enter Reason",
                                maxLines: 4,
                                onChanged: (p0) => setState(() {}),
                              ),
                            )
                          : const SizedBox.shrink(),
                    ),
                    YBox(24),
                    RichText(
                        text: TextSpan(
                      children: [
                        TextSpan(
                          text: "Attach Images. ",
                          style: AppTypography.text14,
                        ),
                        TextSpan(
                          text: "If available",
                          style: AppTypography.text12.withCustomColor(
                              AppColors.black.withValues(alpha: 0.45)),
                        ),
                      ],
                    )),
                    YBox(8),
                    InkWell(
                      onTap: _pickImages,
                      child: Stack(
                        children: [
                          Container(
                            height: Sizer.height(100),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(4),
                              border: Border.all(
                                color: AppColors.black.withValues(alpha: 0.1),
                                width: 1,
                              ),
                            ),
                            child: SvgPicture.asset(
                              AppSvgs.uploadDoc,
                              height: Sizer.height(100),
                              width: Sizer.screenWidth,
                            ),
                          ),
                        ],
                      ),
                    ),
                    YBox(16),
                    if (loadingImage)
                      Center(
                          child: LoaderIcon(
                        color: AppColors.grey,
                        size: Sizer.radius(30),
                      ))
                    else if (_imageFiles.isNotEmpty)
                      ListView.separated(
                        shrinkWrap: true,
                        physics: NeverScrollableScrollPhysics(),
                        itemCount: _imageFiles.length,
                        separatorBuilder: (context, index) => YBox(12),
                        itemBuilder: (context, index) {
                          final file = _imageFiles[index];
                          return Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  ClipRRect(
                                    borderRadius: BorderRadius.circular(4),
                                    child: Image.file(
                                      file,
                                      height: Sizer.height(50),
                                      width: Sizer.height(50),
                                      fit: BoxFit.cover,
                                      errorBuilder:
                                          (context, error, stackTrace) {
                                        return Container(
                                          height: Sizer.height(50),
                                          width: Sizer.height(50),
                                          color: AppColors.neutral5,
                                          child: Icon(
                                            Icons.image,
                                            color: AppColors.black,
                                          ),
                                        );
                                      },
                                    ),
                                  ),
                                  // SizedBox(
                                  //   height: Sizer.height(40),
                                  //   width: Sizer.height(40),
                                  //   child: Image.asset(AppImages.oip),
                                  // ),
                                  XBox(8),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Expanded(
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    " ${file.path.split('/').last}",
                                                    maxLines: 2,
                                                    overflow:
                                                        TextOverflow.ellipsis,
                                                    style: AppTypography.text14,
                                                  ),
                                                  Text(
                                                    "${(file.lengthSync() / 1024).toStringAsFixed(1)}KB",
                                                    style: AppTypography.text12
                                                        .withCustomColor(
                                                      AppColors.black
                                                          .withValues(
                                                              alpha: 0.45),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                            const XBox(40),
                                            InkWell(
                                              onTap: () {
                                                setState(() {
                                                  _imageFiles.removeAt(index);
                                                  _uploadedUrls.removeAt(index);
                                                });
                                              },
                                              child: Icon(
                                                Iconsax.trash,
                                                size: Sizer.radius(16),
                                                color: AppColors.blue8C,
                                              ),
                                            )
                                          ],
                                        ),
                                        YBox(6),
                                        Row(
                                          children: [
                                            Expanded(
                                              child: LinearPercentIndicator(
                                                padding: EdgeInsets.zero,
                                                lineHeight: 8.0,
                                                percent: ref
                                                    .watch(fileUploadVm)
                                                    .getProgressForFile(
                                                        file.path),
                                                backgroundColor:
                                                    AppColors.yellowEC,
                                                progressColor:
                                                    AppColors.yellowOA,
                                                barRadius: Radius.circular(8),
                                                animation: true,
                                                animationDuration: 300,
                                              ),
                                            ),
                                            XBox(12),
                                            Text(
                                              "${(ref.watch(fileUploadVm).getProgressForFile(file.path) * 100).toInt()}%",
                                              style: AppTypography.text12
                                                  .withCustomColor(
                                                      AppColors.gray700),
                                            )
                                          ],
                                        )
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          );
                        },
                      ),
                    YBox(24),
                    if (ref.watch(deliveryVmodel).busy(logComplaintState))
                      BtnLoadState()
                    else
                      Row(
                        children: [
                          Expanded(
                            child: CustomBtn.solid(
                              onTap: () {
                                Navigator.pop(context);
                              },
                              online: true,
                              isOutline: true,
                              textColor:
                                  AppColors.black.withValues(alpha: 0.45),
                              outlineColor:
                                  AppColors.black.withValues(alpha: 0.45),
                              text: "Cancel",
                            ),
                          ),
                          XBox(12),
                          Expanded(
                            child: CustomBtn.solid(
                              onTap: () async {
                                if (_selectedReasonIndex ==
                                        _reasons.length - 1 &&
                                    commentC.text.isEmpty) {
                                  FlushBarToast.fLSnackBar(
                                    snackBarType: SnackBarType.warning,
                                    message: 'Please enter a reason',
                                  );
                                  return;
                                }

                                final r =
                                    await ref.read(deliveryVmodel).logComplaint(
                                          id: widget.deliveryId,
                                          comment: _selectedReasonIndex ==
                                                  _reasons.length - 1
                                              ? commentC.text.trim()
                                              : _reasons[_selectedReasonIndex],
                                          documentUrls: _uploadedUrls,
                                        );

                                handleApiResponse(
                                    response: r,
                                    onSuccess: () {
                                      Navigator.pop(context);
                                      ref
                                          .read(deliveryVmodel)
                                          .viewDelivery(widget.deliveryId);
                                    });
                              },
                              online: true,
                              text: "Log Complaint",
                            ),
                          ),
                        ],
                      ),
                    YBox(30),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _pickImages() async {
    setState(() {
      loadingImage = true;
      _uploadsComplete = false;
    });

    try {
      // Reset progress tracking for any previous uploads
      ref.read(fileUploadVm).resetProgress();

      final pickedFiles = await ImagePickerUtils.pickImage(multiImage: true);

      for (var pickedFile in pickedFiles) {
        setState(() => _imageFiles.add(File(pickedFile.path)));
      }

      if (_imageFiles.isNotEmpty) {
        final r = await ref.read(fileUploadVm).uploadFile(file: _imageFiles);
        if (r.success && r.data != null && r.data!.isNotEmpty) {
          printty("upload complete ${r.data!.first.url}");
          _uploadedUrls.addAll(r.data!.map((e) => e.url ?? ""));
          setState(() {
            _uploadsComplete = true;
          });
        }
      }
    } catch (e) {
      FlushBarToast.fLSnackBar(
          snackBarType: SnackBarType.warning, message: e.toString());
    } finally {
      setState(() => loadingImage = false);
    }
  }
}
