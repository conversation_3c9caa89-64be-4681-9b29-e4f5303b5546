import 'dart:io';

import 'package:builder_konnect_mgt/core/core.dart';
import 'package:builder_konnect_mgt/ui/components/components.dart';
import 'package:percent_indicator/linear_percent_indicator.dart';

class ConfirmPackageModal extends ConsumerStatefulWidget {
  const ConfirmPackageModal({
    super.key,
    required this.arg,
  });

  final PackageArg arg;

  @override
  ConsumerState<ConfirmPackageModal> createState() =>
      _ConfirmPackageModalState();
}

class _ConfirmPackageModalState extends ConsumerState<ConfirmPackageModal> {
  final List<File> _imageFiles = [];
  final List<String> _uploadedUrls = [];
  bool loadingImage = false;
  bool uploadsComplete = false;
  bool hasConfirmed = false;

  @override
  Widget build(BuildContext context) {
    printty("widget.arg.selectedItems ${widget.arg.selectedItems.toString()}");
    return Padding(
      padding: EdgeInsets.only(
        left: Sizer.width(16),
        right: Sizer.width(16),
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      child: ConstrainedBox(
        constraints: BoxConstraints(
          maxHeight: Sizer.screenHeight * 0.8,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            YBox(20),
            Row(
              children: [
                Text("Confirm Package", style: AppTypography.text16.medium),
                Spacer(),
                InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Icon(
                    Icons.close,
                    color: AppColors.black,
                    size: Sizer.radius(24),
                  ),
                )
              ],
            ),
            Flexible(
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    YBox(24),
                    InkWell(
                      onTap: _pickImages,
                      child: Stack(
                        children: [
                          Container(
                            height: Sizer.height(100),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(4),
                              border: Border.all(
                                color: AppColors.black.withValues(alpha: 0.1),
                                width: 1,
                              ),
                            ),
                            child: SvgPicture.asset(
                              AppSvgs.uploadDoc,
                              height: Sizer.height(100),
                              width: Sizer.screenWidth,
                            ),
                          ),
                        ],
                      ),
                    ),
                    YBox(16),
                    if (loadingImage)
                      Center(
                          child: LoaderIcon(
                        color: AppColors.grey,
                        size: Sizer.radius(30),
                      ))
                    else if (_imageFiles.isNotEmpty)
                      ListView.separated(
                        shrinkWrap: true,
                        physics: NeverScrollableScrollPhysics(),
                        itemCount: _imageFiles.length,
                        separatorBuilder: (context, index) => YBox(12),
                        itemBuilder: (context, index) {
                          final file = _imageFiles[index];
                          return Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  ClipRRect(
                                    borderRadius: BorderRadius.circular(4),
                                    child: Image.file(
                                      file,
                                      height: Sizer.height(50),
                                      width: Sizer.height(50),
                                      fit: BoxFit.cover,
                                      errorBuilder:
                                          (context, error, stackTrace) {
                                        return Container(
                                          height: Sizer.height(50),
                                          width: Sizer.height(50),
                                          color: AppColors.neutral5,
                                          child: Icon(
                                            Icons.image,
                                            color: AppColors.black,
                                          ),
                                        );
                                      },
                                    ),
                                  ),
                                  XBox(8),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Expanded(
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    " ${file.path.split('/').last}",
                                                    maxLines: 2,
                                                    overflow:
                                                        TextOverflow.ellipsis,
                                                    style: AppTypography.text14,
                                                  ),
                                                  Text(
                                                    "${(file.lengthSync() / 1024).toStringAsFixed(1)}KB",
                                                    style: AppTypography.text12
                                                        .withCustomColor(
                                                      AppColors.black
                                                          .withValues(
                                                              alpha: 0.45),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                            const XBox(40),
                                            InkWell(
                                              onTap: () {
                                                setState(() {
                                                  _imageFiles.removeAt(index);
                                                  _uploadedUrls.removeAt(index);
                                                });
                                              },
                                              child: Icon(
                                                Iconsax.trash,
                                                size: Sizer.radius(16),
                                                color: AppColors.blue8C,
                                              ),
                                            )
                                          ],
                                        ),
                                        YBox(6),
                                        Row(
                                          children: [
                                            Expanded(
                                              child: LinearPercentIndicator(
                                                padding: EdgeInsets.zero,
                                                lineHeight: 8.0,
                                                percent: ref
                                                    .watch(fileUploadVm)
                                                    .getProgressForFile(
                                                        file.path),
                                                backgroundColor:
                                                    AppColors.yellowEC,
                                                progressColor:
                                                    AppColors.yellowOA,
                                                barRadius: Radius.circular(8),
                                                animation: true,
                                                animationDuration: 300,
                                              ),
                                            ),
                                            XBox(12),
                                            Text(
                                              "${(ref.watch(fileUploadVm).getProgressForFile(file.path) * 100).toInt()}%",
                                              style: AppTypography.text12
                                                  .withCustomColor(
                                                      AppColors.gray700),
                                            )
                                          ],
                                        )
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          );
                        },
                      ),
                    YBox(24),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomCheckbox(
                          onTap: () {
                            hasConfirmed = !hasConfirmed;
                            setState(() {});
                          },
                          isSelected: hasConfirmed,
                        ),
                        XBox(8),
                        Expanded(
                          child: ConfirmRichText(
                            selectedItems: (widget.arg.selectedItems ?? [])
                                .map((e) => e.product ?? "")
                                .toList(),
                            unSelectedItems: (widget.arg.items)
                                .where((e) => !widget.arg.selectedItems!.any(
                                    (i) =>
                                        i.itemShipmentId == e.itemShipmentId))
                                .map((e) => e.product ?? "")
                                .toList(),
                          ),
                        ),
                      ],
                    ),
                    YBox(24),
                    if (ref.watch(deliveryVmodel).busy(updateTrackingState))
                      BtnLoadState()
                    else
                      CustomBtn.solid(
                        onTap: () async {
                          if (!hasConfirmed) {
                            FlushBarToast.fLSnackBar(
                              snackBarType: SnackBarType.warning,
                              message: 'Please confirm the package',
                            );
                            return;
                          }

                          if (_uploadedUrls.isEmpty) {
                            FlushBarToast.fLSnackBar(
                              snackBarType: SnackBarType.warning,
                              message: 'Please attach images',
                            );
                            return;
                          }

                          final r =
                              await ref.read(deliveryVmodel).updateTracking(
                                    id: widget.arg.orderId,
                                    media: _uploadedUrls,
                                    status: AppText.orderConfirmed,
                                    itemShipmentIds: widget.arg.selectedItems
                                        ?.map((e) => e.itemShipmentId ?? "")
                                        .toList(),
                                  );

                          handleApiResponse(
                              response: r,
                              onSuccess: () {
                                Navigator.pop(context);
                                Navigator.pop(context);
                                ref
                                    .read(deliveryVmodel)
                                    .viewDelivery(widget.arg.orderId);
                              });
                        },
                        online: true,
                        isOutline: true,
                        textColor: AppColors.black.withValues(alpha: 0.45),
                        outlineColor: AppColors.black.withValues(alpha: 0.45),
                        text: "Confirm Package",
                      ),
                    YBox(30),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _pickImages() async {
    setState(() {
      loadingImage = true;
      uploadsComplete = false;
    });

    try {
      // Reset progress tracking for any previous uploads
      ref.read(fileUploadVm).resetProgress();

      final pickedFiles = await ImagePickerUtils.pickImage(multiImage: true);

      for (var pickedFile in pickedFiles) {
        setState(() => _imageFiles.add(File(pickedFile.path)));
      }

      if (_imageFiles.isNotEmpty) {
        final r = await ref.read(fileUploadVm).uploadFile(file: _imageFiles);
        if (r.success && r.data != null && r.data!.isNotEmpty) {
          printty("upload complete ${r.data!.first.url}");
          _uploadedUrls.addAll(r.data!.map((e) => e.url ?? ""));
          setState(() {
            uploadsComplete = true;
          });
        }
      }
    } catch (e) {
      FlushBarToast.fLSnackBar(
          snackBarType: SnackBarType.warning, message: e.toString());
    } finally {
      setState(() => loadingImage = false);
    }
  }
}

class ConfirmRichText extends StatelessWidget {
  const ConfirmRichText({
    super.key,
    required this.selectedItems,
    required this.unSelectedItems,
  });

  final List<String> selectedItems;
  final List<String> unSelectedItems;

  @override
  Widget build(BuildContext context) {
    return RichText(
      text: TextSpan(
        style: AppTypography.text12
            .withCustomColor(AppColors.gray600)
            .withCustomHeight(1.4),
        children: [
          TextSpan(
            text:
                "I hereby confirm that I have received the package from the vendor, and upon inspection, I have verified that the following items,\n",
          ),
          ...selectedItems.map(
            (item) => TextSpan(
              text: "$item\n",
              style:
                  AppTypography.text12.withCustomColor(AppColors.primaryBlue),
            ),
          ),
          if (unSelectedItems.isNotEmpty)
            TextSpan(
              text:
                  "are correct, complete, and accurately match the customer’s order specifications. ",
            ),
          if (unSelectedItems.isNotEmpty)
            TextSpan(
              text: " \nWhile ",
            ),
          ...unSelectedItems.map(
            (item) => TextSpan(
              text: "$item ",
              style: AppTypography.text12.withCustomColor(AppColors.red6),
            ),
          ),
          if (unSelectedItems.isNotEmpty)
            TextSpan(
              text: "is  unavailable.",
            ),
        ],
      ),
    );
  }
}
