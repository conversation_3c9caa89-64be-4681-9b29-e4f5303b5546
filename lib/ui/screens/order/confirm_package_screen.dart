import 'package:builder_konnect_mgt/core/core.dart';
import 'package:builder_konnect_mgt/ui/components/components.dart';

class ConfirmPackageScreen extends ConsumerStatefulWidget {
  const ConfirmPackageScreen({
    super.key,
    required this.arg,
  });

  final PackageArg arg;

  @override
  ConsumerState<ConfirmPackageScreen> createState() =>
      _ConfirmPackageScreenState();
}

class _ConfirmPackageScreenState extends ConsumerState<ConfirmPackageScreen> {
  List<LineItem> selectedItems = [];

  @override
  Widget build(BuildContext context) {
    printty("Slected items ${selectedItems.toString()}");
    return Scaffold(
      appBar: CustomAppbar(
        title: "Confirm Package",
      ),
      body: Padding(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(16),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            YBox(18),
            Text(
              "Tick available products to confirm this package",
              style: AppTypography.text14.withCustomColor(
                AppColors.black.withValues(alpha: 0.45),
              ),
            ),
            Expanded(
              child: ListView.separated(
                shrinkWrap: true,
                padding: EdgeInsets.only(
                  top: Sizer.height(24),
                ),
                itemBuilder: (_, i) {
                  final item = widget.arg.items[i];
                  return PackageListTile(
                    item: item,
                    isSelected: selectedItems.contains(item),
                    onSelect: () {
                      selectedItems.contains(item)
                          ? selectedItems.remove(item)
                          : selectedItems.add(item);
                      setState(() {});
                    },
                  );
                },
                separatorBuilder: (_, __) => YBox(16),
                itemCount: widget.arg.items.length,
              ),
            ),
            YBox(24),
            CustomBtn.solid(
              onTap: () {
                if (selectedItems.isEmpty) {
                  FlushBarToast.fLSnackBar(
                    snackBarType: SnackBarType.warning,
                    message: 'Please select at least one item',
                  );
                  return;
                }
                ModalWrapper.bottomSheet(
                    context: context,
                    widget: ConfirmPackageModal(
                      arg: PackageArg(
                        orderId: widget.arg.orderId,
                        items: widget.arg.items,
                        selectedItems: selectedItems,
                      ),
                    ));
              },
              online: true,
              isOutline: true,
              textColor: AppColors.black.withValues(alpha: 0.45),
              outlineColor: AppColors.black.withValues(alpha: 0.45),
              text: "Confirm Package",
            ),
            YBox(30),
          ],
        ),
      ),
    );
  }
}

class PackageListTile extends StatelessWidget {
  const PackageListTile({
    super.key,
    required this.item,
    this.isSelected = false,
    this.onSelect,
  });

  final LineItem item;
  final bool isSelected;
  final VoidCallback? onSelect;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onSelect,
      child: Row(
        children: [
          CustomCheckbox(
            onTap: onSelect,
            isSelected: isSelected,
          ),
          XBox(12),
          SizedBox(
            height: Sizer.height(40),
            width: Sizer.width(40),
            child: MyCachedNetworkImage(
              imageUrl: item.mediaUrl ?? "",
            ),
          ),
          XBox(8),
          Expanded(
            child: Text(
              item.product ?? "",
              style: AppTypography.text14.withCustomColor(
                AppColors.black.withValues(alpha: 0.85),
              ),
            ),
          ),
          Text(
            "${item.quantity ?? 0}X",
            style: AppTypography.text14.withCustomColor(
              AppColors.black.withValues(alpha: 0.45),
            ),
          ),
          XBox(30),
          Text(
            "${item.totalCost ?? 0}",
            style: AppTypography.text14.withCustomColor(
              AppColors.black.withValues(alpha: 0.45),
            ),
          ),
        ],
      ),
    );
  }
}
