import 'package:builder_konnect_mgt/core/core.dart';
import 'package:builder_konnect_mgt/core/utils/transistion_utils.dart';
import 'package:builder_konnect_mgt/ui/screens/screens.dart';

class AppRouter {
  static Route<dynamic> onGenerateRoute(RouteSettings settings) {
    final args = settings.arguments;

    switch (settings.name) {
      case RoutePath.splashScreen:
        return TransitionUtils.buildTransition(
          const SplashScreen(),
          settings,
        );
      case RoutePath.bottomNavScreen:
        return TransitionUtils.buildTransition(
          const BottomNavScreen(),
          settings,
        );

      //  Auth
      case RoutePath.loginScreen:
        return TransitionUtils.buildTransition(
          const LoginScreen(),
          settings,
        );
      case RoutePath.forgotPasswordScreen:
        return TransitionUtils.buildTransition(
          const ForgotPasswordScreen(),
          settings,
        );
      case RoutePath.forgotPasswordOtpScreen:
        if (args is ForgotArg) {
          return TransitionUtils.buildTransition(
            ForgotPasswordOtpScreen(arg: args),
            settings,
          );
        }
        return errorScreen(settings);

      case RoutePath.newPasswordScreen:
        if (args is ForgotArg) {
          return TransitionUtils.buildTransition(
            NewPasswordScreen(arg: args),
            settings,
          );
        }
        return errorScreen(settings);

      // Order
      case RoutePath.orderDetailsScreen:
        if (args is String) {
          return TransitionUtils.buildTransition(
            OrderDetailsScreen(orderId: args),
            settings,
          );
        }
        return errorScreen(settings);

      case RoutePath.confirmPackageScreen:
        if (args is PackageArg) {
          return TransitionUtils.buildTransition(
            ConfirmPackageScreen(arg: args),
            settings,
          );
        }
        return errorScreen(settings);

      case RoutePath.notificationScreen:
        return TransitionUtils.buildTransition(
          const NotificationScreen(),
          settings,
        );

      // Wallet
      case RoutePath.sendMoneyScreen:
        return TransitionUtils.buildTransition(
          const SendMoneyScreen(),
          settings,
        );
      case RoutePath.sendMoneyAccountScreen:
        return TransitionUtils.buildTransition(
          const SendMoneyAccountScreen(),
          settings,
        );
      case RoutePath.sendMoneySuccesScreen:
        return TransitionUtils.buildTransition(
          const SendMoneySuccesScreen(),
          settings,
        );

      default:
        return errorScreen(settings);
    }

    // CupertinoPageRoute buildRoute(Widget widget) =>
    //     CupertinoPageRoute(builder: (_) => widget);

    // switch (settings.name) {
    //   case RoutePath.splashScreen:
    //     return buildRoute(const SplashScreen());
    //   case RoutePath.bottomNavScreen:
    //     return buildRoute(const BottomNavScreen());

    //   //  Auth
    //   case RoutePath.loginScreen:
    //     return buildRoute(const LoginScreen());
    //   case RoutePath.forgotPasswordScreen:
    //     return buildRoute(const ForgotPasswordScreen());
    //   case RoutePath.forgotPasswordOtpScreen:
    //     if (args is ForgotArg) {
    //       return buildRoute(ForgotPasswordOtpScreen(arg: args));
    //     }
    //     return errorScreen('No route defined for ${settings.name}');
    //   case RoutePath.newPasswordScreen:
    //     if (args is ForgotArg) {
    //       return buildRoute(NewPasswordScreen(arg: args));
    //     }
    //     return errorScreen('No route defined for ${settings.name}');

    //   // Order
    //   case RoutePath.orderDetailsScreen:
    //     if (args is String) {
    //       return buildRoute(OrderDetailsScreen(id: args));
    //     }
    //     return errorScreen('No route defined for ${settings.name}');

    //   case RoutePath.notificationScreen:
    //     return buildRoute(const NotificationScreen());

    //   // Wallet
    //   case RoutePath.sendMoneyScreen:
    //     return buildRoute(const SendMoneyScreen());
    //   case RoutePath.sendMoneyAccountScreen:
    //     return buildRoute(const SendMoneyAccountScreen());
    //   case RoutePath.sendMoneySuccesScreen:
    //     return buildRoute(const SendMoneySuccesScreen());

    //   default:
    //     return errorScreen('No route defined for ${settings.name}');
    // }
  }

  static errorScreen(RouteSettings settings) {
    return TransitionUtils.buildTransition(
      ScreenNotFound(routeName: settings.name),
      settings,
    );
  }
}
