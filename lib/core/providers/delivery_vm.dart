import 'package:builder_konnect_mgt/core/core.dart';

const String resolveIssuesState = "resolveIssuesState";
const String attachImagesState = "attachImagesState";
const String updateTrackingState = "updateTrackingState";
const String assignDeliveryState = "assignDeliveryState";
const String getVehicleTypesState = "getVehicleTypesState";
const String getTrackingStatusesState = "getTrackingStatusesState";
const String logComplaintState = "logComplaintState";

class DeliveryVm extends BaseVm {
  List<OrderModel> _orderDeliveries = [];
  List<OrderModel> get orderDeliveries => _orderDeliveries;

  OrderDetailModel? _orderDetailModel;
  OrderDetailModel? get orderDetailModel => _orderDetailModel;

  //page number
  int pageNumber = 1;
  //total records
  int totalRecords = 0;

  Future<ApiResponse> fulfilmentDeliveries({
    String? dateFilter,
    String? status,
    String? query,
    int? sortBy,
    bool isFirstCall = true,
  }) async {
    if (isFirstCall) {
      pageNumber = 1;
    } else {
      pageNumber++;
    }

    UriBuilder uriBuilder = UriBuilder("/api/v1/fulfilment-officers/deliveries")
      ..addQueryParameterIfNotEmpty("date_filter", dateFilter ?? '')
      ..addQueryParameterIfNotEmpty("status", status ?? '')
      ..addQueryParameterIfNotEmpty("q", query ?? '')
      ..addQueryParameterIfNotEmpty("page", pageNumber.toString())
      ..addQueryParameterIfNotEmpty("sort_by", sortBy?.toString() ?? '');

    return await performApiCall(
      url: uriBuilder.build().toString(),
      method: apiService.getWithAuth,
      busyObjectName: isFirstCall ? null : paginateState,
      onSuccess: (data) {
        final orderDeliveries =
            orderModelFromJson(json.encode(data["data"]["data"]));
        totalRecords = orderDeliveries.length;
        if (isFirstCall) {
          _orderDeliveries = orderDeliveries;
        } else {
          _orderDeliveries.addAll(orderDeliveries);
        }
        return apiResponse;
      },
    );
  }

  Future<ApiResponse<OrderDetailModel>> viewDelivery(String id) async {
    return await performApiCall<OrderDetailModel>(
      url: "/api/v1/fulfilment-officers/deliveries/$id",
      method: apiService.getWithAuth,
      onSuccess: (data) {
        _orderDetailModel = null;
        _orderDetailModel = orderDetailModelFromJson(json.encode(data["data"]));
        return ApiResponse(
          success: true,
          data: _orderDetailModel,
        );
      },
    );
  }

  Future<ApiResponse<List<String>>> getVehicleTypes() async {
    return await performApiCall<List<String>>(
      url: "/api/v1/fulfilment-officers/deliveries/all/vehicle-types",
      method: apiService.getWithAuth,
      busyObjectName: getVehicleTypesState,
      onSuccess: (data) {
        if (data is Map && data.containsKey("data") && data["data"] is List) {
          return ApiResponse(
            success: true,
            data: (data["data"] as List).map((e) => e.toString()).toList(),
          );
        }
        return ApiResponse(
          success: false,
          message: "Invalid data format",
          data: [],
        );
      },
    );
  }

  Future<ApiResponse<List<String>>> getTrackingStatuses() async {
    return await performApiCall<List<String>>(
      url: "/api/v1/fulfilment-officers/deliveries/all/statuses",
      method: apiService.getWithAuth,
      busyObjectName: getTrackingStatusesState,
      onSuccess: (data) {
        if (data is Map && data.containsKey("data") && data["data"] is List) {
          return ApiResponse(
            success: true,
            data: (data["data"] as List).map((e) => e.toString()).toList(),
          );
        }
        return ApiResponse(
          success: false,
          message: "Invalid data format",
          data: [],
        );
      },
    );
  }

  Future<ApiResponse> attachImages({
    required String id,
    required List<String> documentUrls,
  }) async {
    return await performApiCall(
      url: "/api/v1/fulfilment-officers/deliveries/$id/attach-media",
      method: apiService.putWithAuth,
      busyObjectName: attachImagesState,
      body: {
        "media": documentUrls,
      },
      onSuccess: (data) {
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> assignDelivery({
    required String id,
    required RiderArg riderArg,
  }) async {
    final body = riderArg.toMap();
    body.removeWhere((k, v) => v == null || v == "");
    return await performApiCall(
      url:
          "/api/v1/fulfilment-officers/deliveries/$id/assign-to-delivery-officer",
      method: apiService.putWithAuth,
      busyObjectName: assignDeliveryState,
      body: body,
      onSuccess: (data) {
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> resolveIssues({
    required String id,
    required String comment,
  }) async {
    return await performApiCall(
      url: "/api/v1/fulfilment-officers/deliveries/$id/add-comment",
      method: apiService.putWithAuth,
      busyObjectName: resolveIssuesState,
      body: {"comment": comment},
      onSuccess: (data) {
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> logComplaint({
    required String id,
    required String comment,
    required List<String> documentUrls,
  }) async {
    return await performApiCall(
      url: "/api/v1/fulfilment-officers/deliveries/$id/add-comment",
      method: apiService.putWithAuth,
      busyObjectName: logComplaintState,
      body: {"comment": comment, "media": documentUrls},
      onSuccess: (data) {
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> updateTracking({
    required String id,
    required String status,
    List<String>? media,
    List<String>? itemShipmentIds,
  }) async {
    printty("itemShipmentIds $itemShipmentIds");
    final body = {
      "status": status,
      "media": media,
      "item_shipment_ids": itemShipmentIds,
    };
    body.removeWhere((k, v) => v == null || v == "");
    printty("body $body");
    return await performApiCall(
      url: "/api/v1/fulfilment-officers/deliveries/$id/status",
      method: apiService.putWithAuth,
      busyObjectName: updateTrackingState,
      body: body,
      onSuccess: (data) {
        return apiResponse;
      },
    );
  }
}

final deliveryVmodel = ChangeNotifierProvider((ref) {
  return DeliveryVm();
});
