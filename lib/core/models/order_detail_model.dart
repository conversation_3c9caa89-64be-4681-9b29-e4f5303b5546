import 'dart:convert';

OrderDetailModel orderDetailModelFromJson(String str) =>
    OrderDetailModel.fromJson(json.decode(str));

String orderDetailModelToJson(OrderDetailModel data) =>
    json.encode(data.toJson());

class OrderDetailModel {
  final String? id;
  final String? salesOrderId;
  final String? orderNumber;
  final String? receiptNo;
  final String? status;
  final DateTime? createdAt;
  final Customer? customer;
  final int? itemsCount;
  final String? totalCost;
  final String? deliveryDate;
  final Vendor? vendor;
  final List<LineItem>? lineItems;
  final Fees? fees;
  final String? subtotal;
  final String? amount;
  final RiderDetails? riderDetails;
  final List<dynamic>? deliveryAttachments;
  final dynamic comment;
  final List<Timeline>? timeline;

  OrderDetailModel({
    this.id,
    this.salesOrderId,
    this.orderNumber,
    this.receiptNo,
    this.status,
    this.createdAt,
    this.customer,
    this.itemsCount,
    this.totalCost,
    this.deliveryDate,
    this.vendor,
    this.lineItems,
    this.fees,
    this.subtotal,
    this.amount,
    this.riderDetails,
    this.deliveryAttachments,
    this.comment,
    this.timeline,
  });

  factory OrderDetailModel.fromJson(Map<String, dynamic> json) =>
      OrderDetailModel(
        id: json["id"],
        salesOrderId: json["sales_order_id"],
        orderNumber: json["order_number"],
        receiptNo: json["receipt_no"],
        status: json["status"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        customer: json["customer"] == null
            ? null
            : Customer.fromJson(json["customer"]),
        itemsCount: json["items_count"],
        totalCost: json["total_cost"],
        deliveryDate: json["delivery_date"],
        vendor: json["vendor"] == null ? null : Vendor.fromJson(json["vendor"]),
        lineItems: json["line_items"] == null
            ? []
            : List<LineItem>.from(
                json["line_items"]!.map((x) => LineItem.fromJson(x))),
        fees: json["fees"] == null ? null : Fees.fromJson(json["fees"]),
        subtotal: json["subtotal"],
        amount: json["amount"],
        riderDetails: json["rider_details"] == null
            ? null
            : RiderDetails.fromJson(json["rider_details"]),
        deliveryAttachments: json["delivery_attachments"] == null
            ? []
            : List<dynamic>.from(json["delivery_attachments"]!.map((x) => x)),
        comment: json["comment"],
        timeline: json["timeline"] == null
            ? []
            : List<Timeline>.from(
                json["timeline"]!.map((x) => Timeline.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "sales_order_id": salesOrderId,
        "order_number": orderNumber,
        "receipt_no": receiptNo,
        "status": status,
        "created_at": createdAt?.toIso8601String(),
        "customer": customer?.toJson(),
        "items_count": itemsCount,
        "total_cost": totalCost,
        "delivery_date": deliveryDate,
        "vendor": vendor?.toJson(),
        "line_items": lineItems == null
            ? []
            : List<dynamic>.from(lineItems!.map((x) => x.toJson())),
        "fees": fees?.toJson(),
        "subtotal": subtotal,
        "amount": amount,
        "rider_details": riderDetails?.toJson(),
        "delivery_attachments": deliveryAttachments == null
            ? []
            : List<dynamic>.from(deliveryAttachments!.map((x) => x)),
        "comment": comment,
        "timeline": timeline == null
            ? []
            : List<dynamic>.from(timeline!.map((x) => x.toJson())),
      };
}

class Customer {
  final String? name;
  final dynamic phone;
  final String? email;
  final String? address;

  Customer({
    this.name,
    this.phone,
    this.email,
    this.address,
  });

  factory Customer.fromJson(Map<String, dynamic> json) => Customer(
        name: json["name"],
        phone: json["phone"],
        email: json["email"],
        address: json["address"],
      );

  Map<String, dynamic> toJson() => {
        "name": name,
        "phone": phone,
        "email": email,
        "address": address,
      };
}

class Fees {
  final num? tax;
  final int? serviceFee;
  final double? deliveryFee;

  Fees({
    this.tax,
    this.serviceFee,
    this.deliveryFee,
  });

  factory Fees.fromJson(Map<String, dynamic> json) => Fees(
        tax: json["tax"],
        serviceFee: json["service_fee"],
        deliveryFee: json["delivery_fee"]?.toDouble(),
      );

  Map<String, dynamic> toJson() => {
        "tax": tax,
        "service_fee": serviceFee,
        "delivery_fee": deliveryFee,
      };
}

class LineItem {
  final String? itemShipmentId;
  final String? mediaUrl;
  final String? product;
  final int? quantity;
  final String? totalCost;
  final bool? reassigned;

  LineItem({
    this.itemShipmentId,
    this.mediaUrl,
    this.product,
    this.quantity,
    this.totalCost,
    this.reassigned,
  });

  factory LineItem.fromJson(Map<String, dynamic> json) => LineItem(
        itemShipmentId: json["item_shipment_id"],
        mediaUrl: json["media_url"],
        product: json["product"],
        quantity: json["quantity"],
        totalCost: json["total_cost"],
        reassigned: json["reassigned"],
      );

  Map<String, dynamic> toJson() => {
        "item_shipment_id": itemShipmentId,
        "media_url": mediaUrl,
        "product": product,
        "quantity": quantity,
        "total_cost": totalCost,
        "reassigned": reassigned,
      };
}

List<RiderDetails> riderDetailsFromJson(String str) => List<RiderDetails>.from(
    json.decode(str).map((x) => RiderDetails.fromJson(x)));

class RiderDetails {
  final String? name;
  final String? email;
  final String? phone;
  final int? bankId;
  final String? accountName;
  final String? vehicleType;
  final String? accountNumber;

  RiderDetails({
    this.name,
    this.email,
    this.phone,
    this.bankId,
    this.accountName,
    this.vehicleType,
    this.accountNumber,
  });

  factory RiderDetails.fromJson(Map<String, dynamic> json) => RiderDetails(
        name: json["name"],
        email: json["email"],
        phone: json["phone"],
        bankId: json["bank_id"],
        accountName: json["account_name"],
        vehicleType: json["vehicle_type"],
        accountNumber: json["account_number"],
      );

  Map<String, dynamic> toJson() => {
        "name": name,
        "email": email,
        "phone": phone,
        "bank_id": bankId,
        "account_name": accountName,
        "vehicle_type": vehicleType,
        "account_number": accountNumber,
      };
}

class Timeline {
  final String? status;
  final String? shortDescription;
  final String? longDescription;
  final String? time;
  final List<String>? media;

  Timeline({
    this.status,
    this.shortDescription,
    this.longDescription,
    this.time,
    this.media,
  });

  factory Timeline.fromJson(Map<String, dynamic> json) => Timeline(
        status: json["status"],
        shortDescription: json["short_description"],
        longDescription: json["long_description"],
        time: json["time"],
        media: json["media"] == null
            ? []
            : List<String>.from(json["media"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "short_description": shortDescription,
        "long_description": longDescription,
        "time": time,
        "media": media == null ? [] : List<dynamic>.from(media!.map((x) => x)),
      };
}

class Vendor {
  final String? name;
  final String? contact;
  final String? address;

  Vendor({
    this.name,
    this.contact,
    this.address,
  });

  factory Vendor.fromJson(Map<String, dynamic> json) => Vendor(
        name: json["name"],
        contact: json["contact"],
        address: json["address"],
      );

  Map<String, dynamic> toJson() => {
        "name": name,
        "contact": contact,
        "address": address,
      };
}

// import 'dart:convert';

// OrderDetailModel orderDetailModelFromJson(String str) =>
//     OrderDetailModel.fromJson(json.decode(str));

// String orderDetailModelToJson(OrderDetailModel data) =>
//     json.encode(data.toJson());

// class OrderDetailModel {
//   final String? id;
//   final String? orderNumber;
//   final String? receiptNo;
//   final String? status;
//   final DateTime? createdAt;
//   final Customer? customer;
//   final String? deliveryDate;
//   final Customer? vendor;
//   final List<LineItem>? lineItems;
//   final Fees? fees;
//   final String? subtotal;
//   final String? amount;
//   final RiderDetails? riderDetails;
//   final List<dynamic>? deliveryAttachments;
//   final dynamic comment;
//   final List<Timeline>? timeline;

//   OrderDetailModel({
//     this.id,
//     this.orderNumber,
//     this.receiptNo,
//     this.status,
//     this.createdAt,
//     this.customer,
//     this.deliveryDate,
//     this.vendor,
//     this.lineItems,
//     this.fees,
//     this.subtotal,
//     this.amount,
//     this.riderDetails,
//     this.deliveryAttachments,
//     this.comment,
//     this.timeline,
//   });

//   factory OrderDetailModel.fromJson(Map<String, dynamic> json) =>
//       OrderDetailModel(
//         id: json["id"],
//         orderNumber: json["order_number"],
//         receiptNo: json["receipt_no"],
//         status: json["status"],
//         createdAt: json["created_at"] == null
//             ? null
//             : DateTime.parse(json["created_at"]),
//         customer: json["customer"] == null
//             ? null
//             : Customer.fromJson(json["customer"]),
//         deliveryDate: json["delivery_date"],
//         vendor:
//             json["vendor"] == null ? null : Customer.fromJson(json["vendor"]),
//         lineItems: json["line_items"] == null
//             ? []
//             : List<LineItem>.from(
//                 json["line_items"]!.map((x) => LineItem.fromJson(x))),
//         fees: json["fees"] == null ? null : Fees.fromJson(json["fees"]),
//         subtotal: json["subtotal"],
//         amount: json["amount"],
//         riderDetails: json["rider_details"] == null
//             ? null
//             : RiderDetails.fromJson(json["rider_details"]),
//         deliveryAttachments: json["delivery_attachments"] == null
//             ? []
//             : List<dynamic>.from(json["delivery_attachments"]!.map((x) => x)),
//         comment: json["comment"],
//         timeline: json["timeline"] == null
//             ? []
//             : List<Timeline>.from(
//                 json["timeline"]!.map((x) => Timeline.fromJson(x))),
//       );

//   Map<String, dynamic> toJson() => {
//         "id": id,
//         "order_number": orderNumber,
//         "receipt_no": receiptNo,
//         "status": status,
//         "created_at": createdAt?.toIso8601String(),
//         "customer": customer?.toJson(),
//         "delivery_date": deliveryDate,
//         "vendor": vendor?.toJson(),
//         "line_items": lineItems == null
//             ? []
//             : List<dynamic>.from(lineItems!.map((x) => x.toJson())),
//         "fees": fees?.toJson(),
//         "subtotal": subtotal,
//         "amount": amount,
//         "rider_details": riderDetails?.toJson(),
//         "delivery_attachments": deliveryAttachments == null
//             ? []
//             : List<dynamic>.from(deliveryAttachments!.map((x) => x)),
//         "comment": comment,
//         "timeline": timeline == null
//             ? []
//             : List<dynamic>.from(timeline!.map((x) => x.toJson())),
//       };
// }

// class Customer {
//   final String? name;
//   final String? contact;
//   final String? address;

//   Customer({
//     this.name,
//     this.contact,
//     this.address,
//   });

//   factory Customer.fromJson(Map<String, dynamic> json) => Customer(
//         name: json["name"],
//         contact: json["contact"],
//         address: json["address"],
//       );

//   Map<String, dynamic> toJson() => {
//         "name": name,
//         "contact": contact,
//         "address": address,
//       };
// }

// class Fees {
//   final String? tax;
//   final int? serviceFee;
//   final double? deliveryFee;

//   Fees({
//     this.tax,
//     this.serviceFee,
//     this.deliveryFee,
//   });

//   factory Fees.fromJson(Map<String, dynamic> json) => Fees(
//         tax: json["tax"],
//         serviceFee: json["service_fee"],
//         deliveryFee: json["delivery_fee"]?.toDouble(),
//       );

//   Map<String, dynamic> toJson() => {
//         "tax": tax,
//         "service_fee": serviceFee,
//         "delivery_fee": deliveryFee,
//       };
// }

// class LineItem {
//   final String? itemShipmentId;
//   final String? mediaUrl;
//   final String? product;
//   final int? quantity;
//   final String? totalCost;
//   final bool? reassigned;

//   LineItem({
//     this.itemShipmentId,
//     this.mediaUrl,
//     this.product,
//     this.quantity,
//     this.totalCost,
//     this.reassigned,
//   });

//   factory LineItem.fromJson(Map<String, dynamic> json) => LineItem(
//         itemShipmentId: json["item_shipment_id"],
//         mediaUrl: json["media_url"],
//         product: json["product"],
//         quantity: json["quantity"],
//         totalCost: json["total_cost"],
//         reassigned: json["reassigned"],
//       );

//   Map<String, dynamic> toJson() => {
//         "item_shipment_id": itemShipmentId,
//         "media_url": mediaUrl,
//         "product": product,
//         "quantity": quantity,
//         "total_cost": totalCost,
//         "reassigned": reassigned,
//       };

//   @override
//   String toString() {
//     return 'LineItem(itemShipmentId: $itemShipmentId, mediaUrl: $mediaUrl, product: $product, quantity: $quantity, totalCost: $totalCost, reassigned: $reassigned)';
//   }
// }

// List<RiderDetails> riderDetailsFromJson(String str) => List<RiderDetails>.from(
//     json.decode(str).map((x) => RiderDetails.fromJson(x)));

// class RiderDetails {
//   final String? name;
//   final String? email;
//   final String? phone;
//   final int? bankId;
//   final String? accountName;
//   final String? vehicleType;
//   final String? accountNumber;

//   RiderDetails({
//     this.name,
//     this.email,
//     this.phone,
//     this.bankId,
//     this.accountName,
//     this.vehicleType,
//     this.accountNumber,
//   });

//   factory RiderDetails.fromJson(Map<String, dynamic> json) => RiderDetails(
//         name: json["name"],
//         email: json["email"],
//         phone: json["phone"],
//         bankId: json["bank_id"],
//         accountName: json["account_name"],
//         vehicleType: json["vehicle_type"],
//         accountNumber: json["account_number"],
//       );

//   Map<String, dynamic> toJson() => {
//         "name": name,
//         "email": email,
//         "phone": phone,
//         "bank_id": bankId,
//         "account_name": accountName,
//         "vehicle_type": vehicleType,
//         "account_number": accountNumber,
//       };
// }

// class Timeline {
//   final String? status;
//   final String? shortDescription;
//   final String? longDescription;
//   final String? time;
//   final List<String>? media;

//   Timeline({
//     this.status,
//     this.shortDescription,
//     this.longDescription,
//     this.time,
//     this.media,
//   });

//   factory Timeline.fromJson(Map<String, dynamic> json) => Timeline(
//         status: json["status"],
//         shortDescription: json["short_description"],
//         longDescription: json["long_description"],
//         time: json["time"],
//         media: json["media"] == null
//             ? []
//             : List<String>.from(json["media"]!.map((x) => x)),
//       );

//   Map<String, dynamic> toJson() => {
//         "status": status,
//         "short_description": shortDescription,
//         "long_description": longDescription,
//         "time": time,
//         "media": media == null ? [] : List<dynamic>.from(media!.map((x) => x)),
//       };
// }
